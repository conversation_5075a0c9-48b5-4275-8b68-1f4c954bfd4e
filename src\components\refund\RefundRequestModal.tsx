'use client';

import React, { useState } from 'react';
import { XMarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useToast } from '@/context/ToastContext';
import { REFUND_REASONS } from '@/types/refund';

interface RefundRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  booking: {
    id: number;
    pet_name: string;
    booking_date: string;
    booking_time: string;
    price: number;
    payment_method: string;
    status: string;
  };
  onRefundRequested?: () => void;
}

export default function RefundRequestModal({
  isOpen,
  onClose,
  booking,
  onRefundRequested
}: RefundRequestModalProps) {
  const [reason, setReason] = useState<string>(REFUND_REASONS.USER_REQUESTED);
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showToast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/bookings/${booking.id}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason,
          notes: notes.trim() || undefined
        })
      });

      const data = await response.json();

      if (data.success) {
        showToast('Refund request submitted successfully', 'success');
        onRefundRequested?.();
        onClose();
      } else {
        showToast(data.error || 'Failed to submit refund request', 'error');
      }
    } catch (error) {
      console.error('Error submitting refund request:', error);
      showToast('Failed to submit refund request', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Submit Refund Request</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Booking Details */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">Booking Details</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <p><span className="font-medium">Pet:</span> {booking.pet_name}</p>
              <p><span className="font-medium">Date:</span> {new Date(booking.booking_date).toLocaleDateString()}</p>
              <p><span className="font-medium">Time:</span> {booking.booking_time}</p>
              <p><span className="font-medium">Amount:</span> ₱{parseFloat(booking.price.toString()).toFixed(2)}</p>
              <p><span className="font-medium">Payment Method:</span> {booking.payment_method.toUpperCase()}</p>
            </div>
          </div>

          {/* Warning */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Refund Request Process:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Your request will be reviewed by our team within 1-2 business days</li>
                  <li>You will receive an email notification once your request is approved</li>
                  <li>Approved refunds take 5-10 business days for GCash payments</li>
                  <li>Cash payment refunds require coordination with our support team</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Reason Selection */}
          <div>
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Refund *
            </label>
            <select
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
              required
            >
              <option value={REFUND_REASONS.USER_REQUESTED}>Customer requested cancellation</option>
              <option value={REFUND_REASONS.SERVICE_UNAVAILABLE}>Service no longer available</option>
              <option value={REFUND_REASONS.TECHNICAL_ISSUE}>Technical issue with booking</option>
              <option value={REFUND_REASONS.DUPLICATE_BOOKING}>Duplicate booking</option>
              <option value={REFUND_REASONS.OTHER}>Other reason</option>
            </select>
          </div>

          {/* Additional Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes (Optional)
            </label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent resize-none"
              placeholder="Please provide any additional details about your refund request..."
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting Request...' : 'Submit Request'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
